<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="16dp"
    app:cardElevation="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Create Update"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:padding="8dp" />

        <!-- Input for update content -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:hint="Update content"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/updateContentInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textMultiLine"
                android:minLines="3"
                android:maxLines="6" />
        </com.google.android.material.textfield.TextInputLayout>

        <!-- Input for update title -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:hint="Title (optional)"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/updateTitleInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:maxLines="1" />
        </com.google.android.material.textfield.TextInputLayout>

        <!-- Input for external link -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:hint="External link (optional)"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/updateLinkInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textUri"
                android:maxLines="1" />
        </com.google.android.material.textfield.TextInputLayout>

        <!-- Media preview -->
        <FrameLayout
            android:id="@+id/mediaPreviewContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:visibility="gone">

            <ImageView
                android:id="@+id/mediaPreview"
                android:layout_width="match_parent"
                android:layout_height="150dp"
                android:scaleType="centerCrop"
                android:contentDescription="Selected media" />

            <ImageButton
                android:id="@+id/removeMediaButton"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_gravity="top|end"
                android:background="@drawable/circle_background"
                android:src="@drawable/ic_close"
                android:contentDescription="Remove media" />
        </FrameLayout>

        <!-- Document preview -->
        <LinearLayout
            android:id="@+id/documentPreviewContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="16dp"
            android:padding="8dp"
            android:background="#F5F5F5"
            android:visibility="gone">

            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_attachment"
                android:contentDescription="Document" />

            <TextView
                android:id="@+id/documentNameText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:layout_gravity="center_vertical"
                android:ellipsize="middle"
                android:singleLine="true"
                android:text="Document name" />

            <ImageButton
                android:id="@+id/removeDocumentButton"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_close"
                android:contentDescription="Remove document" />
        </LinearLayout>

        <!-- Attachment options -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="16dp">

            <ImageButton
                android:id="@+id/attachImageButton"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_gallery"
                android:contentDescription="Attach image" />

            <ImageButton
                android:id="@+id/attachDocumentButton"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_document"
                android:contentDescription="Attach document" />
        </LinearLayout>

        <!-- Cross-post checkbox -->
        <CheckBox
            android:id="@+id/crossPostCheckbox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="Also post to Teaching Wing interface"
            android:textSize="14sp"
            android:visibility="gone" />

        <!-- Action buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="16dp">

            <Button
                android:id="@+id/cancelButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Cancel"
                style="@style/Widget.MaterialComponents.Button.TextButton" />

            <Button
                android:id="@+id/postUpdateButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Post Update"
                style="@style/Widget.MaterialComponents.Button.UnelevatedButton" />
        </LinearLayout>

    </LinearLayout>
</androidx.cardview.widget.CardView> 