package com.phad.chatapp.ui.profile

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.phad.chatapp.R

data class ProfileUiState(
    val name: String = "Loading...",
    val location: String = "...",
    val email: String = "loading...",
    val phone: String = "loading...",
    val rollNumber: String = "loading...",
    val collegeEmail: String = "loading...",
    val academicGroup: String = "loading...",
    val nssGroup: String = "loading...",
    val topic1: String = "loading...",
    val topic2: String = "loading...",
    val topic3: String = "loading...",
    val userType: String = "Student",
    val events: String = "-/-",
    val classes: String = "-/-",
    val meetings: String = "-/-",
    val isStudent: Boolean = true,
    val Teaching_wing: Boolean = false,
    // Additional student fields from database
    val courseCode: String = "loading...",
    val gmailId: String = "loading...",
    val instituteId: String = "loading...",
    val subjectPreference1: String = "loading...",
    val subjectPreference2: String = "loading...",
    val subjectPreference3: String = "loading...",
    val teachingWingStatus: String = "loading...",
    val profileImageUrl: String = ""
)

@Composable
fun ProfileScreen(
    modifier: Modifier = Modifier,
    state: ProfileUiState,
    onLogoutClick: () -> Unit,
    onChatbotClick: () -> Unit,
    onLibraryClick: () -> Unit,
    onChatClick: () -> Unit,
    onScheduleClick: () -> Unit,
    onSwitchInterfaceClick: () -> Unit,
    currentInterface: String,
    teachingWing: Boolean
) {
    val isDarkTheme = isSystemInDarkTheme()
    val backgroundColor = Color(0xff0d0302)
    val surfaceColor =Color.White
    val onSurfaceColor = Color.Black
    val onSurfaceVariantColor = onSurfaceColor.copy(alpha = 0.6f)
    val onBackgroundColor =  Color.White

    Surface(
        modifier = modifier.fillMaxSize(),
        color = backgroundColor
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            // Top Section with background images and stats
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(450.dp) // Adjusted height for responsiveness
            ) {
                Image(
                    painter = painterResource(id = R.drawable.vector271),
                    contentDescription = null,
                    contentScale = ContentScale.FillBounds,
                    modifier = Modifier.fillMaxSize()
                )
                Image(
                    painter = painterResource(id = R.drawable.vector272),
                    contentDescription = null,
                    contentScale = ContentScale.FillBounds,
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(start = 50.dp, end = 50.dp, top = 20.dp, bottom = 100.dp)
                        .clip(RoundedCornerShape(150.dp))
                )

                // Header with logo and icons
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 24.dp, vertical = 24.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.thelogosmall),
                        contentDescription = "The Logo Small",
                        colorFilter = ColorFilter.tint(Color(0xffffcc00)),
                        modifier = Modifier.size(width = 72.dp, height = 40.dp)
                    )
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        // Show switch icon for any user with teachingWing == true
                        if (teachingWing) {
                            val switchTargetText = if (currentInterface == "Teaching Wing") "NSS" else "Teaching Wing"
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                                modifier = Modifier
                                    .clickable { onSwitchInterfaceClick() }
                                    .padding(horizontal = 4.dp)
                            ) {
                                Image(
                                    painter = painterResource(id = R.drawable.switch_account),
                                    contentDescription = "Switch to $switchTargetText",
                                    colorFilter = ColorFilter.tint(onBackgroundColor),
                                    modifier = Modifier.size(30.dp)
                                )
                            }
                            Spacer(modifier = Modifier.size(12.dp))
                        }

                        Image(
                            painter = painterResource(id = R.drawable.ic_library),
                            contentDescription = "Library Icon",
                            colorFilter = ColorFilter.tint(onBackgroundColor),
                            modifier = Modifier
                                .size(30.dp)
                                .clickable { onLibraryClick() }
                        )
                        Spacer(modifier = Modifier.size(16.dp))
                        Image(
                            painter = painterResource(id = R.drawable.ic_robot),
                            contentDescription = "Chatbot Icon",
                            colorFilter = ColorFilter.tint(onBackgroundColor),
                            modifier = Modifier
                                .size(30.dp)
                                .clickable { onChatbotClick() }
                        )
                    }
                }
                
                // Admin Profile Image (centered in header space) - Only for Admin users
                if (!state.isStudent) {
                    android.util.Log.d("ProfileScreen", "Admin user detected. isStudent: ${state.isStudent}, profileImageUrl: '${state.profileImageUrl}'")
                    
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 100.dp, bottom = 60.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        // White background circle for better contrast
                        Box(
                            modifier = Modifier
                                .size(130.dp)
                                .clip(CircleShape)
                                .background(Color.White)
                                .border(4.dp, Color(0xFFFFCC00), CircleShape),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProfileImage(
                                imageUrl = state.profileImageUrl,
                                size = 120.dp,
                                borderColor = Color(0xFFFFCC00) // Golden border
                            )
                        }
                    }
                } else {
                    android.util.Log.d("ProfileScreen", "Student user detected, not showing profile image")
                }

                // Stats Section
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .align(Alignment.BottomCenter)
                        .padding(bottom = 80.dp), // Pushed up to overlap the card
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    StatItem(label = "Events", value = state.events, color = onBackgroundColor)
                    StatItem(label = "Classes", value = state.classes, size = 48.sp, color = onBackgroundColor)
                    StatItem(label = "Meetings", value = state.meetings, color = onBackgroundColor)
                }
            }

            // Bottom Section with profile details
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(topStart = 40.dp, topEnd = 40.dp))
                    .background(surfaceColor)
                    .padding(24.dp)
            ) {
                // Name section without duplicate email and phone
                Text(
                    text = state.name,
                    color = onSurfaceColor,
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(24.dp))

                if (state.isStudent) {
                    // Student Information Section
                    Text(
                        text = "Academic Information",
                        color = onSurfaceVariantColor,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(bottom = 12.dp)
                    )
                    
                    // First row: Roll Number and Academic Group
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Column(modifier = Modifier.weight(1f)) {
                            LabeledInfoItem(label = "Roll Number", value = state.rollNumber, color = onSurfaceColor)
                        }
                        Column(modifier = Modifier.weight(1f)) {
                            LabeledInfoItem(label = "Academic Group", value = state.academicGroup, color = onSurfaceColor)
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // Second row: Course Code (if available) and NSS Group
                    if (state.courseCode.isNotEmpty() && state.courseCode != "loading...") {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Column(modifier = Modifier.weight(1f)) {
                                LabeledInfoItem(label = "Course Code", value = state.courseCode, color = onSurfaceColor)
                            }
                            Column(modifier = Modifier.weight(1f)) {
                                LabeledInfoItem(label = "NSS Group", value = state.nssGroup, color = onSurfaceColor)
                            }
                        }
                    } else {
                        // Only show NSS Group if Course Code is hidden
                        LabeledInfoItem(label = "NSS Group", value = state.nssGroup, color = onSurfaceColor)
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // Contact Information Section
                    Text(
                        text = "Contact Information",
                        color = onSurfaceVariantColor,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(bottom = 12.dp)
                    )
                    
                    // First row: Gmail ID and Mobile Number
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Column(modifier = Modifier.weight(1f)) {
                            LabeledInfoItem(label = "Gmail ID", value = state.gmailId, color = onSurfaceColor)
                        }
                        Column(modifier = Modifier.weight(1f)) {
                            LabeledInfoItem(label = "Mobile Number", value = state.phone, color = onSurfaceColor)
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // Second row: Institute ID (full width)
                    LabeledInfoItem(label = "Institute ID", value = state.instituteId, color = onSurfaceColor)
                    
                    // Subject Preferences Section - Only show if preferences are available
                    if (state.subjectPreference1.isNotEmpty() && state.subjectPreference1 != "loading..." &&
                        state.subjectPreference2.isNotEmpty() && state.subjectPreference2 != "loading..." &&
                        state.subjectPreference3.isNotEmpty() && state.subjectPreference3 != "loading...") {
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        Text(
                            text = "Subject Preferences",
                            color = onSurfaceVariantColor,
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.padding(bottom = 12.dp)
                        )
                        
                        LabeledInfoItem(label = "1st Preference", value = state.subjectPreference1, color = onSurfaceColor)
                        LabeledInfoItem(label = "2nd Preference", value = state.subjectPreference2, color = onSurfaceColor)
                        LabeledInfoItem(label = "3rd Preference", value = state.subjectPreference3, color = onSurfaceColor)
                    }
                } else { // Admin
                    // Admin Information Section
                    Text(
                        text = "Professional Information",
                        color = onSurfaceVariantColor,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(bottom = 12.dp)
                    )
                    
                    // Only Roll Number (removed Name)
                    LabeledInfoItem(label = "Roll Number", value = state.rollNumber, color = onSurfaceColor)
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // Contact Information Section
                    Text(
                        text = "Contact Information",
                        color = onSurfaceVariantColor,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(bottom = 12.dp, top = 16.dp)
                    )
                    
                    // Second row: College Email and Contact Number
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Column(modifier = Modifier.weight(1f)) {
                            LabeledInfoItem(label = "College Email", value = state.collegeEmail, color = onSurfaceColor)
                        }
                        Column(modifier = Modifier.weight(1f)) {
                            LabeledInfoItem(label = "Contact Number", value = state.phone, color = onSurfaceColor)
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // Personal Email (full width)
                    LabeledInfoItem(label = "Personal Email", value = state.email, color = onSurfaceColor)
                }

                Spacer(modifier = Modifier.height(32.dp))

                // Logout Button
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(12.dp))
                        .background(onSurfaceVariantColor.copy(alpha = 0.1f))
                        .clickable { onLogoutClick() }
                        .padding(vertical = 16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.logout),
                            contentDescription = "Logout Icon",
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.size(8.dp))
                        Text(
                            text = "Log Out",
                            color = Color.Red,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }

            // Add a spacer at the bottom to push content above the nav bar
            Spacer(modifier = Modifier.height(80.dp))
        }
    }
}

@Composable
fun StatItem(label: String, value: String, size: androidx.compose.ui.unit.TextUnit = 32.sp, color: Color) {
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Text(text = value, color = color, fontSize = size, fontWeight = FontWeight.Bold)
        Text(text = label, color = color.copy(alpha = 0.7f), fontSize = 12.sp)
    }
}

@Composable
fun ProfileDetailItem(label: String, value: String, color: Color) {
    Column(modifier = Modifier.padding(vertical = 4.dp)) {
        if (label.isNotEmpty()) {
            Text(
                text = label,
                color = color,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold
            )
        }
        Text(
            text = value,
            color = color.copy(alpha = 0.7f),
            fontSize = 14.sp,
            fontWeight = FontWeight.SemiBold
        )
    }
}

@Composable
fun LabeledInfoItem(label: String, value: String, color: Color) {
    Column(
        modifier = Modifier
            .padding(vertical = 6.dp)
            .padding(end = 8.dp)
    ) {
        Text(
            text = label,
            color = color.copy(alpha = 0.7f),
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium
        )
        Text(
            text = value,
            color = color,
            fontSize = 16.sp,
            fontWeight = FontWeight.SemiBold
        )
    }
}

@Composable
fun CircularProfileImage(
    imageUrl: String,
    size: androidx.compose.ui.unit.Dp,
    borderColor: Color
) {
    // Convert Google Drive URL to direct image URL if needed
    fun convertGoogleDriveUrl(url: String): String {
        return if (url.contains("drive.google.com/file/d/")) {
            val fileIdPattern = Regex("drive\\.google\\.com/file/d/([a-zA-Z0-9_-]+)")
            val matchResult = fileIdPattern.find(url)
            if (matchResult != null) {
                val fileId = matchResult.groupValues[1]
                "https://drive.google.com/uc?export=view&id=$fileId"
            } else {
                url
            }
        } else {
            url
        }
    }
    
    val processedImageUrl = if (imageUrl.isNotEmpty()) convertGoogleDriveUrl(imageUrl) else ""
    
    android.util.Log.d("CircularProfileImage", "Original URL: '$imageUrl'")
    android.util.Log.d("CircularProfileImage", "Processed URL: '$processedImageUrl'")
    
    Box(
        modifier = Modifier
            .size(size)
            .clip(CircleShape)
            .background(Color.White) // White background for better contrast
            .border(2.dp, borderColor, CircleShape),
        contentAlignment = Alignment.Center
    ) {
        if (processedImageUrl.isNotEmpty()) {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(processedImageUrl)
                    .crossfade(true)
                    .listener(
                        onStart = { android.util.Log.d("CircularProfileImage", "Image loading started for: $processedImageUrl") },
                        onSuccess = { _, _ -> android.util.Log.d("CircularProfileImage", "Image loaded successfully") },
                        onError = { _, result -> android.util.Log.e("CircularProfileImage", "Image loading failed: ${result.throwable}") }
                    )
                    .build(),
                contentDescription = "Profile Picture",
                contentScale = ContentScale.Crop,
                modifier = Modifier
                    .size(size - 4.dp) // Slightly smaller to show the border and background
                    .clip(CircleShape),
                error = painterResource(id = R.drawable.ic_person), // Use person icon instead of robot
                placeholder = painterResource(id = R.drawable.ic_person) // Use person icon instead of robot
            )
        } else {
            // Default profile icon
            Icon(
                imageVector = Icons.Default.Person,
                contentDescription = "Default Profile",
                tint = Color.Gray,
                modifier = Modifier.size(size * 0.6f)
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun ProfilePreview() {
    MaterialTheme {
        ProfileScreen(
            state = ProfileUiState(name = "Tashya Aryan", location = "N/A"),
            onLogoutClick = {},
            onChatbotClick = {},
            onLibraryClick = {},
            onChatClick = {},
            onScheduleClick = {},
            onSwitchInterfaceClick = {},
            currentInterface = "Teaching Wing",
            teachingWing = true
        )
    }
}