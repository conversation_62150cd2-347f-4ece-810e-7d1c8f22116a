package com.phad.chatapp.fragments

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.FieldValue
import com.phad.chatapp.LoginActivity
import com.phad.chatapp.R
import com.phad.chatapp.ui.profile.ProfileScreen
import com.phad.chatapp.ui.profile.ProfileUiState
import com.phad.chatapp.utils.SessionManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await

class NssProfileFragment : Fragment() {
    private val TAG = "NssProfileFragment"
    private lateinit var sessionManager: SessionManager
    
    private val _uiState = MutableStateFlow(ProfileUiState())
    private val uiState: StateFlow<ProfileUiState> = _uiState.asStateFlow()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                val state by uiState.collectAsState()
                val teachingWing = sessionManager.getTeachingWing()
                ProfileScreen(
                    state = state,
                    onLogoutClick = { logout() },
                    onChatbotClick = {
                        val intent = Intent(requireContext(), com.phad.chatapp.features.home.chatbot.ui.ChatBotActivity::class.java)
                        startActivity(intent)
                    },
                    onLibraryClick = {
                        findNavController().navigate(R.id.action_nssProfileFragment_to_nssLibraryItemListFragment)
                    },
                    onChatClick = {},
                    onScheduleClick = {},
                    onSwitchInterfaceClick = {
                        val currentInterface = "NSS"
                        if (teachingWing) {
                            if (currentInterface == "NSS") {
                                sessionManager.setLastInterfaceChoice("TEACHING_WING")
                                val intent = Intent(requireContext(), com.phad.chatapp.MainActivity::class.java)
                                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                                startActivity(intent)
                            } else {
                                sessionManager.setLastInterfaceChoice("NSS")
                                val intent = Intent(requireContext(), com.phad.chatapp.NssMainActivity::class.java)
                                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                                startActivity(intent)
                            }
                        }
                    },
                    currentInterface = "NSS",
                    teachingWing = teachingWing
                )
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // Initialize session manager
        sessionManager = SessionManager(requireContext())
        
        // Load user profile from session
        loadProfileFromSession()
    }
    
    private fun logout() {
        Log.d(TAG, "Logout button clicked")
        try {
            // Clear session data
                sessionManager.logoutUser()

            // Sign out from Firebase
            FirebaseAuth.getInstance().signOut()

            // Navigate to LoginActivity
            val intent = Intent(requireContext(), LoginActivity::class.java)
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                startActivity(intent)
            requireActivity().finish()
            } catch (e: Exception) {
                Log.e(TAG, "Error during logout: ${e.message}", e)
                Toast.makeText(requireContext(), "Logout failed: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }
        
    // Call this after login, after marking attendance (students), or from chat tab refresh (admins)
    fun refreshAttendanceStats() {
        val userType = sessionManager.fetchUserType()
        val rollNumber = sessionManager.fetchUserId()
        val db = FirebaseFirestore.getInstance()
        val metaRef = db.collection("meta").document("statistics")
        if (userType == "Student") {
            val studentRef = db.collection("Student").document(rollNumber)
            lifecycleScope.launch {
                try {
                    val studentDoc = studentRef.get().await()
                    val attended = studentDoc.getLong("event_attendance") ?: 0
                    val metaDoc = metaRef.get().await()
                    val total = metaDoc.getLong("total_events") ?: 0
                    val stats = "$attended/$total"
                    _uiState.update { it.copy(events = stats) }
                    sessionManager.saveAttendanceStats(stats)
                } catch (e: Exception) {
                    _uiState.update { it.copy(events = "0/0") }
                    sessionManager.saveAttendanceStats("0/0")
                    Log.e(TAG, "Error refreshing attendance statistics", e)
                }
            }
        } else { // Admin or other
            lifecycleScope.launch {
                try {
                    val metaDoc = metaRef.get().await()
                    val total = metaDoc.getLong("total_events") ?: 0
                    val stats = "-/$total"
                    _uiState.update { it.copy(events = stats) }
                    sessionManager.saveAttendanceStats(stats)
                } catch (e: Exception) {
                    _uiState.update { it.copy(events = "-/0") }
                    sessionManager.saveAttendanceStats("-/0")
                    Log.e(TAG, "Error refreshing admin event statistics", e)
                }
            }
        }
    }

    private fun loadProfileFromSession() {
        val profile = sessionManager.getProfileFromSession()
        val attendanceStats = sessionManager.fetchAttendanceStats()
        val userType = sessionManager.fetchUserType()
        
        // For both Student and Admin users, load enhanced profile data from Firestore
        if (userType == "Student") {
            loadEnhancedStudentProfile(profile.copy(events = attendanceStats))
        } else {
            // For Admin users, load enhanced profile data from NSS_ADMINS collection
            loadEnhancedAdminProfile(profile.copy(events = attendanceStats))
        }
    }
    
    private fun loadEnhancedStudentProfile(baseProfile: ProfileUiState) {
        val rollNumber = sessionManager.fetchUserId()
        val db = FirebaseFirestore.getInstance()
        
        lifecycleScope.launch {
            try {
                val studentDoc = db.collection("Student").document(rollNumber).get().await()
                
                if (studentDoc.exists()) {
                    val enhancedProfile = baseProfile.copy(
                        // Basic information
                        name = studentDoc.getString("Name") ?: baseProfile.name,
                        rollNumber = studentDoc.getString("Roll_No_") ?: baseProfile.rollNumber,
                        
                        // Academic information
                        academicGroup = "Academic Group: ${studentDoc.getString("Academic_Grp_") ?: "N/A"}",
                        nssGroup = "NSS Group: ${studentDoc.getString("NSS_gro") ?: "N/A"}",
                        
                        // Contact information
                        gmailId = studentDoc.getString("Gmail_ID") ?: "N/A",
                        instituteId = studentDoc.getString("Institute_ID") ?: "N/A",
                        phone = studentDoc.getString("Mobile_no_") ?: baseProfile.phone,
                        
                        // Note: Subject preferences and course code are not loaded for NSS interface
                        subjectPreference1 = "", // Hidden in NSS interface
                        subjectPreference2 = "", // Hidden in NSS interface 
                        subjectPreference3 = "", // Hidden in NSS interface
                        courseCode = "", // Hidden in NSS interface
                        
                        // Teaching Wing status
                        teachingWingStatus = if (studentDoc.getBoolean("Teaching_wing") == true) "Teaching Wing" else "Not Applicable"
                    )
                    
                    _uiState.value = enhancedProfile
                    Log.d(TAG, "Enhanced student profile loaded successfully for NSS interface")
                    
                } else {
                    Log.w(TAG, "Student document not found, using base profile")
                    _uiState.value = baseProfile
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Error loading enhanced student profile", e)
                _uiState.value = baseProfile
            }
        }
    }
    
    private fun loadEnhancedAdminProfile(baseProfile: ProfileUiState) {
        val rollNumber = sessionManager.fetchUserId()
        val db = FirebaseFirestore.getInstance()
        
        lifecycleScope.launch {
            try {
                val adminDoc = db.collection("NSS_ADMINS").document(rollNumber).get().await()
                
                if (adminDoc.exists()) {
                    val imageUrl = adminDoc.getString("image_url") ?: ""
                    Log.d(TAG, "NSS Admin document found. Image URL: '$imageUrl'")
                    Log.d(TAG, "NSS Admin document data: ${adminDoc.data}")
                    
                    val enhancedProfile = baseProfile.copy(
                        // Basic information
                        name = adminDoc.getString("Name") ?: baseProfile.name,
                        rollNumber = adminDoc.getString("Roll_Number") ?: baseProfile.rollNumber,
                        
                        // Contact information
                        collegeEmail = adminDoc.getString("College_Email") ?: "N/A",
                        phone = adminDoc.getString("Contact_Number") ?: baseProfile.phone,
                        email = adminDoc.getString("Personal_Email") ?: baseProfile.email,
                        
                        // Profile image
                        profileImageUrl = imageUrl,
                        
                        // Mark as not student (Admin)
                        isStudent = false
                    )
                    
                    _uiState.value = enhancedProfile
                    Log.d(TAG, "Enhanced admin profile loaded successfully for NSS interface")
                    
                } else {
                    Log.w(TAG, "Admin document not found, using base profile")
                    _uiState.value = baseProfile.copy(isStudent = false)
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Error loading enhanced admin profile", e)
                _uiState.value = baseProfile.copy(isStudent = false)
            }
        }
    }
    
    private fun loadStatistics(rollNumber: String?) {
        val userType = sessionManager.fetchUserType()
        if (userType != "Student") {
            _uiState.update { it.copy(events = "-/-") }
            return
        }
        if (rollNumber == null) {
            Log.e(TAG, "Cannot load statistics: Roll number is null")
            return
        }
        val db = FirebaseFirestore.getInstance()
        val studentRef = db.collection("Student").document(rollNumber)
        val metaRef = db.collection("meta").document("statistics")
        lifecycleScope.launch {
            try {
                val studentDoc = studentRef.get().await()
                val attended = studentDoc.getLong("event_attendance") ?: 0
                val metaDoc = metaRef.get().await()
                val total = metaDoc.getLong("total_events") ?: 0
                _uiState.update { it.copy(events = "$attended/$total") }
            } catch (e: Exception) {
                _uiState.update { it.copy(events = "0/0") }
                Log.e(TAG, "Error loading attendance statistics", e)
            }
        }

        // Load classes statistics
        db.collection("classes")
            .whereEqualTo("userId", rollNumber)
            .get()
            .addOnSuccessListener { documents ->
                val totalClasses = documents.size()
                val completedClasses = documents.count { it.getString("status") == "COMPLETED" }
                _uiState.update { it.copy(classes = "$completedClasses/$totalClasses") }
                Log.d(TAG, "Loaded classes statistics: $completedClasses/$totalClasses")
            }
            .addOnFailureListener {
                _uiState.update { it.copy(classes = "0/0") }
                Log.e(TAG, "Error loading classes statistics")
            }
        
        // Load meetings statistics
        db.collection("meetings")
            .whereEqualTo("rollNumber", rollNumber)
            .get()
            .addOnSuccessListener { documents ->
                val totalMeetings = documents.size()
                val attendedMeetings = documents.count { it.getBoolean("attended") == true }
                _uiState.update { it.copy(meetings = "$attendedMeetings/$totalMeetings") }
                Log.d(TAG, "Loaded meetings statistics: $attendedMeetings/$totalMeetings")
            }
            .addOnFailureListener {
                _uiState.update { it.copy(meetings = "0/0") }
                Log.e(TAG, "Error loading meetings statistics")
            }
    }


}