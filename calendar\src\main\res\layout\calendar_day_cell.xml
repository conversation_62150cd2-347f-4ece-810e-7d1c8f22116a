<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cardDayCell"
    android:layout_width="match_parent"
    android:layout_height="48dp"
    android:layout_margin="2dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground"
    app:cardBackgroundColor="@android:color/white"
    app:cardCornerRadius="4dp"
    app:cardElevation="2dp">

    <TextView
        android:id="@+id/tvDayNumber"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:textColor="@android:color/black"
        android:textSize="14sp" />

</androidx.cardview.widget.CardView> 