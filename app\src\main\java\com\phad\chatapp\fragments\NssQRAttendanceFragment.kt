package com.phad.chatapp.fragments

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.ui.layout.ContentScale
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.QrCode
import androidx.compose.material.icons.filled.Stop
import androidx.compose.material.icons.filled.People
import androidx.compose.material.icons.filled.Refresh

import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.AccessTime
import androidx.compose.material.icons.filled.Description
import androidx.compose.material.icons.filled.ExpandMore
import androidx.compose.material.icons.filled.ExpandLess
import androidx.compose.material.icons.filled.LocationOn
import androidx.compose.material.icons.filled.Star
import androidx.compose.foundation.clickable
import java.util.Date
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.TextStyle
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.phad.chatapp.R
import com.phad.chatapp.models.AttendanceEvent
import com.phad.chatapp.utils.SessionManager
import com.phad.chatapp.viewmodels.QRAttendanceViewModel
import com.phad.chatapp.viewmodels.QRAttendanceViewModelFactory
import kotlinx.coroutines.launch

/**
 * Fragment for Admin QR Attendance - Take Attendance functionality
 * Only accessible to Admin users in NSS interface
 */
class NssQRAttendanceFragment : Fragment() {
    private val TAG = "NssQRAttendanceFragment"
    
    private lateinit var viewModel: QRAttendanceViewModel
    private lateinit var sessionManager: SessionManager
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        sessionManager = SessionManager(requireContext())
        
        // Check if user is admin
        val userType = sessionManager.fetchUserType()
        Log.d(TAG, "User type from SessionManager: '$userType'")

        // Check for Admin (consistent with ViewModel logic)
        if (userType != "Admin") {
            Log.w(TAG, "Non-admin user trying to access QR attendance: '$userType'")
            Toast.makeText(requireContext(), "Access denied. Admin privileges required.", Toast.LENGTH_LONG).show()
            // Navigate back or close fragment
            parentFragmentManager.popBackStack()
            return
        }

        Log.d(TAG, "Admin access granted for user type: '$userType'")
        
        // Initialize ViewModel
        val factory = QRAttendanceViewModelFactory(requireActivity().application)
        viewModel = ViewModelProvider(this, factory)[QRAttendanceViewModel::class.java]
        
        Log.d(TAG, "NssQRAttendanceFragment created for admin: ${sessionManager.fetchUserName()}")
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                val uiState by viewModel.adminUiState.collectAsState()
                
                QRAttendanceAdminScreen(
                    uiState = uiState,
                    onEventSelected = { event ->
                        viewModel.startAttendanceSession(event)
                    },
                    onEndSession = {
                        viewModel.endAttendanceSession()
                    },
                    onLoadEvents = {
                        viewModel.loadAvailableEvents()
                    },
                    onClearError = {
                        viewModel.clearError()
                    },
                    onCreateEvent = { name, description, location, date, openingTime, closingTime, hours ->
                        viewModel.createAttendanceEvent(name, description, location, date, openingTime, closingTime, hours)
                    },
                    onShowCreateDialog = {
                        viewModel.showCreateEventDialog()
                    },
                    onHideCreateDialog = {
                        viewModel.hideCreateEventDialog()
                    },
                    onClearCreateSuccess = {
                        viewModel.clearCreateEventSuccess()
                    },
                    onCloseEvent = { event ->
                        viewModel.closeEvent(event)
                    },
                    onNavigateBack = {
                        findNavController().navigateUp()
                    }
                )
            }
        }
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Force refresh user info to ensure proper admin status
        viewModel.refreshUserInfo()

        // Load available events when fragment is created
        viewModel.loadAvailableEvents()

        // Observe error messages and UI state changes
        lifecycleScope.launch {
            viewModel.adminUiState.collect { state ->
                Log.d(TAG, "UI State changed: isAdmin=${state.isAdmin}, isSessionActive=${state.isSessionActive}, adminId='${state.adminId}', adminName='${state.adminName}'")

                state.errorMessage?.let { error ->
                    Toast.makeText(requireContext(), error, Toast.LENGTH_LONG).show()
                    viewModel.clearError()
                }
            }
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        // End any active session when leaving the fragment
        lifecycleScope.launch {
            val currentState = viewModel.adminUiState.value
            if (currentState.isSessionActive) {
                viewModel.endAttendanceSession()
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun QRAttendanceAdminScreen(
    uiState: com.phad.chatapp.viewmodels.AdminQRUiState,
    onEventSelected: (AttendanceEvent) -> Unit,
    onEndSession: () -> Unit,
    onLoadEvents: () -> Unit,
    onClearError: () -> Unit,
    onCreateEvent: (String, String, String, Date, Date, Date, Int) -> Unit,
    onShowCreateDialog: () -> Unit,
    onHideCreateDialog: () -> Unit,
    onClearCreateSuccess: () -> Unit,
    onCloseEvent: (AttendanceEvent) -> Unit,
    onNavigateBack: () -> Unit = {}
) {
    // Handle success message
    val context = LocalContext.current
    LaunchedEffect(uiState.createEventSuccess) {
        if (uiState.createEventSuccess) {
            // Show success toast
            Toast.makeText(
                context,
                "Event created successfully",
                Toast.LENGTH_SHORT
            ).show()
            onClearCreateSuccess()
        }
    }

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        floatingActionButton = {
            // Show FAB only on event selection screen (not during active session) and for admin users
            if (!uiState.isSessionActive && uiState.isAdmin) {
                FloatingActionButton(
                    onClick = onShowCreateDialog,
                    containerColor = Color(0xFF2196F3),
                    contentColor = Color.White
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "Create New Event"
                    )
                }
            }
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(Color(0xFFF5F5F5))
                .padding(paddingValues)
                .padding(16.dp)
        ) {
            // Header with back button
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = Color(0xFF2196F3))
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    // Back button on the left
                    if (!uiState.isLoading) {
                        IconButton(
                            onClick = {
                                if (uiState.isSessionActive) {
                                    onEndSession() // End session if active
                                } else {
                                    onNavigateBack() // Navigate back to previous screen
                                }
                            },
                            modifier = Modifier.size(48.dp)
                        ) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "Back",
                                tint = Color.White,
                                modifier = Modifier.size(28.dp)
                            )
                        }
                    } else {
                        // Empty space to maintain layout balance when loading
                        Spacer(modifier = Modifier.size(48.dp))
                    }

                    // Center content with QR icon and title
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.weight(1f),
                        horizontalArrangement = Arrangement.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.QrCode,
                            contentDescription = "QR Attendance",
                            tint = Color.White,
                            modifier = Modifier.size(32.dp)
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Text(
                            text = "QR Attendance",
                            color = Color.White,
                            fontSize = 20.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }

                    // Empty space on the right to balance the back button
                    Spacer(modifier = Modifier.size(48.dp))
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            if (uiState.isLoading) {
                // Loading state
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            } else if (!uiState.isSessionActive) {
                // Event selection screen
                EventSelectionScreen(
                    events = uiState.availableEvents,
                    onEventSelected = onEventSelected,
                    onRefresh = onLoadEvents,
                    onCloseEvent = onCloseEvent
                )
            } else {
                // Active session screen
                ActiveSessionScreen(
                    uiState = uiState,
                    onEndSession = onEndSession
                )
            }
        }
    }

    // Create Event Dialog
    if (uiState.showCreateEventDialog) {
        CreateEventDialog(
            isCreating = uiState.isCreatingEvent,
            onCreateEvent = onCreateEvent,
            onDismiss = onHideCreateDialog,
            errorMessage = uiState.errorMessage
        )
    }
}

@Composable
fun EventSelectionScreen(
    events: List<AttendanceEvent>,
    onEventSelected: (AttendanceEvent) -> Unit,
    onRefresh: () -> Unit,
    onCloseEvent: ((AttendanceEvent) -> Unit)? = null
) {
    Column {
        // Header with refresh button
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Select Event for Attendance",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold
            )
            
            IconButton(onClick = onRefresh) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = "Refresh Events"
                )
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        if (events.isEmpty()) {
            // No events available - with proper bottom padding for floating navigation bar
            Column {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(containerColor = Color(0xFFFFF3E0))
                ) {
                    Column(
                        modifier = Modifier.padding(24.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "No Active Events",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "There are no live events available for attendance. Please check back later or create a new event.",
                            fontSize = 14.sp,
                            textAlign = TextAlign.Center,
                            color = Color.Gray
                        )
                    }
                }
                // Add bottom spacer for floating navigation bar and FAB
                Spacer(modifier = Modifier.height(96.dp)) // 56dp nav height + 20dp margin + 20dp FAB space
            }
        } else {
            // Events list with proper bottom padding for floating navigation bar
            LazyColumn(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(8.dp),
                contentPadding = PaddingValues(
                    bottom = 96.dp // 56dp nav height + 20dp margin + 20dp FAB space
                )
            ) {
                items(events) { event ->
                    EventCard(
                        event = event,
                        onSelect = { onEventSelected(event) },
                        onCloseEvent = onCloseEvent
                    )
                }
            }
        }
    }
}

@Composable
fun EventCard(
    event: AttendanceEvent,
    onSelect: () -> Unit,
    onCloseEvent: ((AttendanceEvent) -> Unit)? = null
) {
    var isDescriptionExpanded by remember { mutableStateOf(false) }
    val maxDescriptionLength = 100

    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            // Event name with improved typography
            Text(
                text = event.getEventName(),
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                color = Color(0xFF1A1A1A)
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Event description with expand/collapse functionality
            if (event.description.isNotEmpty()) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.Top
                ) {
                    Icon(
                        imageVector = Icons.Default.Description,
                        contentDescription = "Description",
                        tint = Color(0xFF666666),
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Column(modifier = Modifier.weight(1f)) {
                        val displayText = if (event.description.length > maxDescriptionLength && !isDescriptionExpanded) {
                            "${event.description.take(maxDescriptionLength)}..."
                        } else {
                            event.description
                        }

                        Text(
                            text = displayText,
                            fontSize = 14.sp,
                            color = Color(0xFF666666),
                            lineHeight = 20.sp
                        )

                        if (event.description.length > maxDescriptionLength) {
                            TextButton(
                                onClick = { isDescriptionExpanded = !isDescriptionExpanded },
                                modifier = Modifier.padding(0.dp),
                                contentPadding = PaddingValues(0.dp)
                            ) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Text(
                                        text = if (isDescriptionExpanded) "Show less" else "Show more",
                                        fontSize = 12.sp,
                                        color = Color(0xFF2196F3)
                                    )
                                    Icon(
                                        imageVector = if (isDescriptionExpanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                                        contentDescription = null,
                                        tint = Color(0xFF2196F3),
                                        modifier = Modifier.size(16.dp)
                                    )
                                }
                            }
                        }
                    }
                }
                Spacer(modifier = Modifier.height(12.dp))
            }

            // Event details with improved visual hierarchy
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // Date and time information with left-right alignment (prominent styling)
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    // Date section on the left
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.DateRange,
                            contentDescription = "Date",
                            tint = Color(0xFF4CAF50),
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = event.getFormattedEventDate(),
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF333333)
                        )
                    }

                    // Time section on the right
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.AccessTime,
                            contentDescription = "Time",
                            tint = Color(0xFF2196F3),
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = event.getFormattedTimeRange(),
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF333333)
                        )
                    }
                }

                // Location information (if available)
                if (event.location.isNotBlank()) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.LocationOn,
                            contentDescription = "Location",
                            tint = Color(0xFFE91E63),
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = event.location,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF333333)
                        )
                    }
                }

                // Hours and Attendees row
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    // Hours section on the left
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Star,
                            contentDescription = "Hours",
                            tint = Color(0xFFFFC107),
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "Hours: ${event.hours}",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF333333)
                        )
                    }

                    // Attendees section on the right
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.People,
                            contentDescription = "Attendees",
                            tint = Color(0xFFFF9800),
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "Attendees",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF333333)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        // Attendee count badge
                        Surface(
                            shape = RoundedCornerShape(12.dp),
                            color = Color(0xFFFF9800)
                        ) {
                            Text(
                                text = "${event.totalMarked}",
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Bold,
                                color = Color.White,
                                modifier = Modifier.padding(horizontal = 12.dp, vertical = 4.dp)
                            )
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(20.dp))

            // Action buttons with improved styling
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp, Alignment.CenterHorizontally)
            ) {
                Button(
                    onClick = onSelect,
                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF4CAF50)),
                    shape = RoundedCornerShape(8.dp),
                    modifier = Modifier.height(48.dp)
                ) {
                    Text(
                        text = "Start Attendance",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium
                    )
                }

                // Show close button only if event is live and callback is provided
                if (event.getEventStatus() == AttendanceEvent.STATUS_LIVE && onCloseEvent != null) {
                    Button(
                        onClick = { onCloseEvent(event) },
                        colors = ButtonDefaults.buttonColors(containerColor = Color(0xFFFF5722)),
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier.height(48.dp)
                    ) {
                        Text(
                            text = "Close Event",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun ActiveSessionScreen(
    uiState: com.phad.chatapp.viewmodels.AdminQRUiState,
    onEndSession: () -> Unit
) {
    Column(
        modifier = Modifier.padding(bottom = 76.dp) // 56dp nav height + 20dp margin
    ) {
        // Enhanced session info header with better visual hierarchy
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color(0xFF4CAF50)),
            shape = RoundedCornerShape(12.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 6.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                // Event name with improved typography
                Text(
                    text = uiState.selectedEvent?.getEventName() ?: "Unknown Event",
                    color = Color.White,
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Event details with combined date-time and attendees
                uiState.selectedEvent?.let { event ->
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        // Date and time with left-right alignment (prominent styling)
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // Date section on the left
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    imageVector = Icons.Default.DateRange,
                                    contentDescription = "Date",
                                    tint = Color.White.copy(alpha = 0.9f),
                                    modifier = Modifier.size(18.dp)
                                )
                                Spacer(modifier = Modifier.width(6.dp))
                                Text(
                                    text = event.getFormattedEventDate(),
                                    color = Color.White.copy(alpha = 0.9f),
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight.Medium
                                )
                            }

                            // Time section on the right
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    imageVector = Icons.Default.AccessTime,
                                    contentDescription = "Time",
                                    tint = Color.White.copy(alpha = 0.9f),
                                    modifier = Modifier.size(18.dp)
                                )
                                Spacer(modifier = Modifier.width(6.dp))
                                Text(
                                    text = event.getFormattedTimeRange(),
                                    color = Color.White.copy(alpha = 0.9f),
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight.Medium
                                )
                            }
                        }

                        // Hours and Attendees row
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // Hours section
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Star,
                                    contentDescription = "Hours",
                                    tint = Color.White.copy(alpha = 0.9f),
                                    modifier = Modifier.size(18.dp)
                                )
                                Spacer(modifier = Modifier.width(6.dp))
                                Text(
                                    text = "Hours: ${event.hours}",
                                    color = Color.White.copy(alpha = 0.9f),
                                    fontSize = 13.sp,
                                    fontWeight = FontWeight.Medium
                                )
                            }

                            // Attendees section with enhanced badge
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    imageVector = Icons.Default.People,
                                    contentDescription = "Attendees",
                                    tint = Color.White.copy(alpha = 0.9f),
                                    modifier = Modifier.size(18.dp)
                                )
                                Spacer(modifier = Modifier.width(6.dp))
                                Text(
                                    text = "Attendees:",
                                    color = Color.White.copy(alpha = 0.9f),
                                    fontSize = 13.sp,
                                    fontWeight = FontWeight.Medium
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Surface(
                                    shape = RoundedCornerShape(12.dp),
                                    color = Color.White.copy(alpha = 0.2f)
                                ) {
                                    Text(
                                        text = "${uiState.attendeeCount}",
                                        color = Color.White,
                                        fontSize = 14.sp,
                                        fontWeight = FontWeight.Bold,
                                        modifier = Modifier.padding(horizontal = 10.dp, vertical = 4.dp)
                                    )
                                }
                            }
                        }
                    }
                }


            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // QR Code display
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Descriptive text at the top
                Text(
                    text = "Scan QR to mark your Attendance",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    textAlign = TextAlign.Center,
                    color = Color.Black,
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(16.dp))

                // QR Code Image - enhanced size for projection visibility, perfectly centered horizontally
                uiState.currentQRCode?.let { bitmap ->
                    Image(
                        bitmap = bitmap.asImageBitmap(),
                        contentDescription = "QR Code for Attendance",
                        contentScale = ContentScale.Fit,
                        modifier = Modifier
                            .size(400.dp)
                            .wrapContentWidth(Alignment.CenterHorizontally)
                    )
                } ?: run {
                    // Loading placeholder - enhanced size matching QR code, perfectly centered horizontally
                    Box(
                        modifier = Modifier
                            .size(400.dp)
                            .wrapContentWidth(Alignment.CenterHorizontally),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(40.dp)
                        )
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CreateEventDialog(
    isCreating: Boolean,
    onCreateEvent: (String, String, String, Date, Date, Date, Int) -> Unit,
    onDismiss: () -> Unit,
    errorMessage: String?,
    initialDate: Date? = null
) {
    var eventName by remember { mutableStateOf("") }
    var eventDescription by remember { mutableStateOf("") }
    var eventLocation by remember { mutableStateOf("") }
    var eventHours by remember { mutableStateOf("0") }
    var selectedDate by remember { mutableStateOf(initialDate ?: Date()) }
    var openingTime by remember {
        mutableStateOf(com.phad.chatapp.utils.AttendanceEventUtils.createTimeFromHourMinute(Date(), 9, 0))
    }
    var closingTime by remember {
        mutableStateOf(com.phad.chatapp.utils.AttendanceEventUtils.createTimeFromHourMinute(Date(), 17, 0))
    }
    var showDatePicker by remember { mutableStateOf(false) }
    var showOpeningTimePicker by remember { mutableStateOf(false) }
    var showClosingTimePicker by remember { mutableStateOf(false) }
    var showError by remember { mutableStateOf(false) }
    var validationErrorMessage by remember { mutableStateOf("") }

    // Reset error state when dialog opens
    LaunchedEffect(Unit) {
        showError = false
    }

    // Show error if there's an error message
    LaunchedEffect(errorMessage) {
        showError = !errorMessage.isNullOrEmpty()
    }

    AlertDialog(
        onDismissRequest = {
            if (!isCreating) {
                onDismiss()
            }
        },
        title = {
            Text(
                text = "Create New Event",
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column {
                // Event Name Field
                OutlinedTextField(
                    value = eventName,
                    onValueChange = {
                        eventName = it
                        showError = false
                    },
                    label = { Text("Event Name *") },
                    placeholder = { Text("Enter event name") },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !isCreating,
                    isError = showError && eventName.trim().isEmpty(),
                    supportingText = {
                        if (showError && eventName.trim().isEmpty()) {
                            Text(
                                text = "Event name is required",
                                color = MaterialTheme.colorScheme.error
                            )
                        }
                    }
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Event Date Field with prominent styling
                OutlinedTextField(
                    value = java.text.SimpleDateFormat("dd MMM yyyy", java.util.Locale.getDefault()).format(selectedDate),
                    onValueChange = { },
                    label = {
                        Text(
                            "Event Date",
                            fontWeight = FontWeight.Medium,
                            fontSize = 14.sp
                        )
                    },
                    placeholder = { Text("Select event date") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { showDatePicker = true },
                    enabled = false,
                    readOnly = true,
                    textStyle = TextStyle(
                        fontWeight = FontWeight.Medium,
                        fontSize = 14.sp,
                        color = Color(0xFF333333)
                    ),
                    trailingIcon = {
                        Icon(
                            imageVector = Icons.Default.DateRange,
                            contentDescription = "Select Date",
                            tint = Color(0xFF4CAF50) // Green color for date
                        )
                    }
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Time Pickers Row
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // Opening Time Field with prominent styling
                    OutlinedTextField(
                        value = com.phad.chatapp.utils.AttendanceEventUtils.formatTimeForPicker(openingTime),
                        onValueChange = { },
                        label = {
                            Text(
                                "Opening Time",
                                fontWeight = FontWeight.Medium,
                                fontSize = 14.sp
                            )
                        },
                        placeholder = { Text("Select opening time") },
                        modifier = Modifier
                            .weight(1f)
                            .clickable { showOpeningTimePicker = true },
                        enabled = false,
                        readOnly = true,
                        textStyle = TextStyle(
                            fontWeight = FontWeight.Medium,
                            fontSize = 14.sp,
                            color = Color(0xFF333333)
                        ),
                        trailingIcon = {
                            Icon(
                                imageVector = Icons.Default.AccessTime,
                                contentDescription = "Select Opening Time",
                                tint = Color(0xFF2196F3) // Blue color for time
                            )
                        }
                    )

                    // Closing Time Field with prominent styling
                    OutlinedTextField(
                        value = com.phad.chatapp.utils.AttendanceEventUtils.formatTimeForPicker(closingTime),
                        onValueChange = { },
                        label = {
                            Text(
                                "Closing Time",
                                fontWeight = FontWeight.Medium,
                                fontSize = 14.sp
                            )
                        },
                        placeholder = { Text("Select closing time") },
                        modifier = Modifier
                            .weight(1f)
                            .clickable { showClosingTimePicker = true },
                        enabled = false,
                        readOnly = true,
                        textStyle = TextStyle(
                            fontWeight = FontWeight.Medium,
                            fontSize = 14.sp,
                            color = Color(0xFF333333)
                        ),
                        trailingIcon = {
                            Icon(
                                imageVector = Icons.Default.AccessTime,
                                contentDescription = "Select Closing Time",
                                tint = Color(0xFF2196F3) // Blue color for time
                            )
                        }
                    )
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Hours Field with prominent styling
                OutlinedTextField(
                    value = eventHours,
                    onValueChange = { newValue ->
                        // Only allow digits and ensure non-negative
                        if (newValue.isEmpty() || (newValue.all { it.isDigit() } && newValue.toIntOrNull()?.let { it >= 0 } == true)) {
                            eventHours = newValue
                            showError = false
                        }
                    },
                    label = {
                        Text(
                            "Hours",
                            fontWeight = FontWeight.Medium,
                            fontSize = 14.sp
                        )
                    },
                    placeholder = { Text("Enter volunteer hours") },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !isCreating,
                    textStyle = TextStyle(
                        fontWeight = FontWeight.Medium,
                        fontSize = 14.sp,
                        color = Color(0xFF333333)
                    ),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    trailingIcon = {
                        Icon(
                            imageVector = Icons.Default.Star,
                            contentDescription = "Hours",
                            tint = Color(0xFFFFC107) // Golden color for hours
                        )
                    },
                    isError = showError && eventHours.trim().isEmpty(),
                    supportingText = {
                        if (showError && eventHours.trim().isEmpty()) {
                            Text(
                                text = "Hours value is required",
                                color = MaterialTheme.colorScheme.error
                            )
                        }
                    }
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Location Field with prominent styling
                OutlinedTextField(
                    value = eventLocation,
                    onValueChange = {
                        eventLocation = it
                        showError = false
                    },
                    label = {
                        Text(
                            "Location (Optional)",
                            fontWeight = FontWeight.Medium,
                            fontSize = 14.sp
                        )
                    },
                    placeholder = { Text("Enter event location") },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !isCreating,
                    textStyle = TextStyle(
                        fontWeight = FontWeight.Medium,
                        fontSize = 14.sp,
                        color = Color(0xFF333333)
                    ),
                    trailingIcon = {
                        Icon(
                            imageVector = Icons.Default.LocationOn,
                            contentDescription = "Location",
                            tint = Color(0xFFE91E63) // Pink color for location
                        )
                    }
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Event Description Field
                OutlinedTextField(
                    value = eventDescription,
                    onValueChange = { eventDescription = it },
                    label = { Text("Description (Optional)") },
                    placeholder = { Text("Enter event description") },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !isCreating,
                    minLines = 2,
                    maxLines = 4
                )

                // Error message
                if (showError) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = if (validationErrorMessage.isNotEmpty()) validationErrorMessage else (errorMessage ?: "Unknown error"),
                        color = MaterialTheme.colorScheme.error,
                        fontSize = 14.sp
                    )
                }

                // Loading indicator
                if (isCreating) {
                    Spacer(modifier = Modifier.height(16.dp))
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(20.dp),
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "Creating event...",
                            fontSize = 14.sp,
                            color = Color.Gray
                        )
                    }
                }
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    val trimmedName = eventName.trim()
                    val trimmedHours = eventHours.trim()
                    val hoursValue = trimmedHours.toIntOrNull() ?: -1

                    when {
                        trimmedName.isEmpty() -> {
                            showError = true
                            validationErrorMessage = "Event name is required"
                        }
                        trimmedHours.isEmpty() || hoursValue < 0 -> {
                            showError = true
                            validationErrorMessage = "Please enter valid hours (0 or greater)"
                        }
                        !com.phad.chatapp.utils.AttendanceEventUtils.validateEventTimes(openingTime, closingTime) -> {
                            showError = true
                            validationErrorMessage = "Closing time must be after opening time"
                        }
                        !com.phad.chatapp.utils.AttendanceEventUtils.validateOpeningTimeNotInPast(selectedDate, openingTime) -> {
                            showError = true
                            validationErrorMessage = "Opening time cannot be in the past"
                        }
                        else -> {
                            showError = false
                            validationErrorMessage = ""
                            onCreateEvent(trimmedName, eventDescription.trim(), eventLocation.trim(), selectedDate, openingTime, closingTime, hoursValue)
                        }
                    }
                },
                enabled = !isCreating,
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF2196F3))
            ) {
                Text("Create Event")
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismiss,
                enabled = !isCreating
            ) {
                Text("Cancel")
            }
        }
    )

    // Date Picker Dialog
    if (showDatePicker) {
        val context = LocalContext.current
        LaunchedEffect(showDatePicker) {
            val calendar = java.util.Calendar.getInstance()
            calendar.time = selectedDate

            val datePickerDialog = android.app.DatePickerDialog(
                context,
                { _, year, month, dayOfMonth ->
                    val newCalendar = java.util.Calendar.getInstance()
                    newCalendar.set(year, month, dayOfMonth)
                    selectedDate = newCalendar.time

                    // Time objects don't need to be updated when date changes
                    // since we now store date and time separately

                    showDatePicker = false
                },
                calendar.get(java.util.Calendar.YEAR),
                calendar.get(java.util.Calendar.MONTH),
                calendar.get(java.util.Calendar.DAY_OF_MONTH)
            )

            datePickerDialog.setOnDismissListener {
                showDatePicker = false
            }

            datePickerDialog.show()
        }
    }

    // Opening Time Picker Dialog
    if (showOpeningTimePicker) {
        val context = LocalContext.current
        LaunchedEffect(showOpeningTimePicker) {
            val timePickerDialog = android.app.TimePickerDialog(
                context,
                { _, hourOfDay, minute ->
                    openingTime = com.phad.chatapp.utils.AttendanceEventUtils.createTimeFromHourMinute(
                        Date(), hourOfDay, minute
                    )
                    showOpeningTimePicker = false
                },
                com.phad.chatapp.utils.AttendanceEventUtils.getHourFromDate(openingTime),
                com.phad.chatapp.utils.AttendanceEventUtils.getMinuteFromDate(openingTime),
                false // Use 12-hour format
            )

            timePickerDialog.setOnDismissListener {
                showOpeningTimePicker = false
            }

            timePickerDialog.show()
        }
    }

    // Closing Time Picker Dialog
    if (showClosingTimePicker) {
        val context = LocalContext.current
        LaunchedEffect(showClosingTimePicker) {
            val timePickerDialog = android.app.TimePickerDialog(
                context,
                { _, hourOfDay, minute ->
                    closingTime = com.phad.chatapp.utils.AttendanceEventUtils.createTimeFromHourMinute(
                        Date(), hourOfDay, minute
                    )
                    showClosingTimePicker = false
                },
                com.phad.chatapp.utils.AttendanceEventUtils.getHourFromDate(closingTime),
                com.phad.chatapp.utils.AttendanceEventUtils.getMinuteFromDate(closingTime),
                false // Use 12-hour format
            )

            timePickerDialog.setOnDismissListener {
                showClosingTimePicker = false
            }

            timePickerDialog.show()
        }
    }
}
